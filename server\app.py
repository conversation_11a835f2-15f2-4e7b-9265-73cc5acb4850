import logging
import os

import uvicorn

from server.api import create_chat_completion, list_models, root, userscript, websocket_endpoint_userscript
from server.api.aistudio import AiStudioAPIHandler
from server.api.aistudio_ws import handle_aistudio_userscript_websocket
from server.core import ModelList, app
from server.core.connection_manager import manager
from server.core.models import ChatCompletionRequest

logger = logging.getLogger(__name__)


# --- API Key 认证 ---
SERVER_OWN_API_KEY = os.environ.get("API_KEY")


def register_routers():
    # --- 注册FastAPI 路由 ---

    # 原有的 Gemini Userscript WebSocket 连接
    app.websocket("/ws/userscript")(websocket_endpoint_userscript)

    # AI Studio Userscript WebSocket 连接
    app.websocket("/ws/aistudio")(handle_aistudio_userscript_websocket)

    # 创建 AI Studio API 处理器并注册消息处理器
    aistudio_handler = AiStudioAPIHandler(manager)
    manager.add_message_handler(aistudio_handler.handle_userscript_message)

    # 模型列表 API
    app.get("/v1/models", response_model=ModelList)(list_models)

    # 聊天完成 API - 支持 AI Studio
    async def aistudio_chat_completions(request: ChatCompletionRequest):
        return await aistudio_handler.create_chat_completion(request)

    app.post("/v1/chat/completions")(create_chat_completion)
    app.post("/v1/aistudio/chat/completions")(aistudio_chat_completions)

    app.get("/")(root)
    app.get("/userscript")(userscript)


def run(host: str = "0.0.0.0", port: int = 8000):
    register_routers()

    if SERVER_OWN_API_KEY:
        logger.info(f"服务器自身 API_KEY 已通过环境变量设置 (前5位): {SERVER_OWN_API_KEY[:5]}...")
    else:
        logger.warning(
            "警告: 服务器自身 API_KEY (环境变量 API_KEY) 未设置。如果 Userscript 也未设置 API Key，则 API 将无保护。"
        )

    raise SystemExit(uvicorn.run(app, host=host, port=port, log_level="info"))
