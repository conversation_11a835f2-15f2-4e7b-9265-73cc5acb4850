/**
 * AI Studio 2 API - DOM Controller
 *
 * 控制 AI Studio 页面的 DOM 元素操作
 */

import type { UIElements, ModelInfo, GenerationConfig } from '../types';
import { DOM_SELECTORS, FALLBACK_SELECTORS, TIMEOUTS } from '../config/constants';
import {
  logger,
  waitForElements,
  isElementVisible,
  isElementInteractable,
  simulateInput,
  simulateClick,
  simulateEvent,
  delay
} from '../utils/helpers';
import { DOMError } from '../types';

export class DOMController {
  private elements: UIElements = {};
  private isInitialized = false;
  private modelList: ModelInfo[] = [];

  constructor() {
    this.bindMethods();
  }

  /**
   * 绑定方法到实例
   */
  private bindMethods(): void {
    this.findElement = this.findElement.bind(this);
    this.findElements = this.findElements.bind(this);
    this.waitForPageReady = this.waitForPageReady.bind(this);
  }

  /**
   * 初始化 DOM 控制器
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('初始化 DOM 控制器...');

      await this.waitForPageReady();
      await this.findAllElements();

      this.isInitialized = true;
      logger.info('DOM 控制器初始化完成');
    } catch (error) {
      throw new DOMError(
        `DOM 控制器初始化失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error }
      );
    }
  }

  /**
   * 等待页面准备就绪
   */
  public async waitForPageReady(): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('页面加载超时'));
      }, TIMEOUTS.DOM_READY_TIMEOUT);

      const checkReady = () => {
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
          clearTimeout(timeout);
          resolve();
        } else {
          setTimeout(checkReady, 100);
        }
      };

      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          clearTimeout(timeout);
          resolve();
        }, { once: true });
      } else {
        checkReady();
      }
    });
  }

  /**
   * 查找单个元素
   */
  public async findElement(
    selector: string,
    fallbacks: string[] = [],
    timeout: number = TIMEOUTS.ELEMENT_WAIT
  ): Promise<Element> {
    const selectors = [selector, ...fallbacks];

    try {
      return await waitForElements(selectors, timeout);
    } catch (error) {
      throw new DOMError(
        `未找到元素: ${selector}`,
        { selector, fallbacks, timeout }
      );
    }
  }

  /**
   * 查找多个元素
   */
  public findElements(selector: string): NodeListOf<Element> {
    return document.querySelectorAll(selector);
  }

  /**
   * 查找所有必要的元素
   */
  private async findAllElements(): Promise<void> {
    try {
      // 查找输入框
      this.elements.promptInput = await this.findElement(
        DOM_SELECTORS.promptInput,
        [...FALLBACK_SELECTORS.promptInput]
      ) as HTMLTextAreaElement;

      // 查找提交按钮
      this.elements.submitButton = await this.findElement(
        DOM_SELECTORS.submitButton,
        [...FALLBACK_SELECTORS.submitButton]
      ) as HTMLButtonElement;

      // 查找响应容器（可选）
      try {
        this.elements.responseContainer = await this.findElement(
          DOM_SELECTORS.responseContainer,
          [],
          3000
        ) as HTMLElement;
      } catch {
        logger.warn('未找到响应容器，将在需要时重新查找');
      }

      // 查找模型选择器（可选）
      try {
        this.elements.modelSelector = await this.findElement(
          DOM_SELECTORS.modelSelector,
          [...FALLBACK_SELECTORS.modelSelector],
          3000
        ) as HTMLSelectElement;
      } catch {
        logger.warn('未找到模型选择器，模型切换功能可能不可用');
      }

      logger.info('成功找到主要 DOM 元素');
    } catch (error) {
      throw new DOMError(
        `查找 DOM 元素失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error }
      );
    }
  }

  /**
   * 发送消息到 AI Studio
   */
  public async sendMessage(message: string): Promise<void> {
    if (!this.isInitialized) {
      throw new DOMError('DOM 控制器未初始化');
    }

    if (!this.elements.promptInput) {
      throw new DOMError('未找到输入框元素');
    }

    if (!isElementInteractable(this.elements.promptInput)) {
      throw new DOMError('输入框不可交互');
    }

    try {
      logger.info(`发送消息: ${message.substring(0, 50)}...`);

      // 清空输入框并输入新消息
      if (this.elements.promptInput instanceof HTMLTextAreaElement ||
        this.elements.promptInput instanceof HTMLInputElement) {
        simulateInput(this.elements.promptInput, message);
      } else if (this.elements.promptInput.contentEditable === 'true') {
        this.elements.promptInput.textContent = message;
        simulateEvent(this.elements.promptInput, 'input');
      } else {
        throw new DOMError('不支持的输入框类型');
      }

      await delay(100); // 等待输入处理

      // 点击提交按钮
      await this.submitMessage();

      logger.info('消息发送成功');
    } catch (error) {
      throw new DOMError(
        `发送消息失败: ${error instanceof Error ? error.message : String(error)}`,
        { message, originalError: error }
      );
    }
  }

  /**
   * 提交消息
   */
  public async submitMessage(): Promise<void> {
    if (!this.elements.submitButton) {
      throw new DOMError('未找到提交按钮');
    }

    if (!isElementInteractable(this.elements.submitButton)) {
      throw new DOMError('提交按钮不可交互');
    }

    try {
      simulateClick(this.elements.submitButton);
      await delay(200); // 等待提交处理
    } catch (error) {
      throw new DOMError(
        `提交消息失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error }
      );
    }
  }

  /**
   * 切换模型 - 按照原脚本的逻辑实现
   */
  public async switchModel(modelId: string): Promise<void> {
    if (!this.modelList.length) {
      throw new DOMError('模型列表未加载');
    }

    try {
      logger.info(`正在尝试将 AI Studio 模型切换为 ${modelId}...`);

      // 1. 找到 AI Studio 页面的模型选择器并点击以展开选项
      const studioModelSelector = document.querySelector('mat-select[data-test-ms-model-selector]');
      if (!studioModelSelector) {
        throw new DOMError('未找到 AI Studio 页面的模型选择器');
      }

      simulateClick(studioModelSelector);
      await delay(500); // 等待下拉菜单动画

      // 2. 从模型列表中找到 modelId 对应的 displayName
      let modelDisplayName = '';
      const modelEntry = this.modelList.find(m =>
        m.id === modelId || m.id.endsWith(modelId) || m.name === modelId
      );

      if (modelEntry) {
        modelDisplayName = modelEntry.displayName;
      } else {
        // 如果在模型列表中找不到，尝试直接使用 modelId
        modelDisplayName = modelId.includes('/') ? modelId.split('/')[1] : modelId;
        modelDisplayName = modelDisplayName
          .replace(/-/g, ' ')
          .replace(/\b\w/g, (l) => l.toUpperCase());
        logger.warn(`未在模型列表中找到 ${modelId} 的确切显示名称，将尝试使用 '${modelDisplayName}'`);
      }

      // 3. 在展开的选项中找到包含目标模型显示名称的 mat-option
      const optionsPane = document.querySelector('div.cdk-overlay-pane');
      if (!optionsPane) {
        await delay(1000); // 额外等待
        const optionsPaneRetry = document.querySelector('div.cdk-overlay-pane');
        if (!optionsPaneRetry) {
          throw new DOMError('未找到模型选项面板 (cdk-overlay-pane)');
        }
      }

      // 查找所有选项
      const allOptions = document.querySelectorAll(
        'div.cdk-overlay-pane mat-option, div.cdk-overlay-pane div[role="option"]'
      );

      logger.info(`尝试匹配显示名称: "${modelDisplayName}"`);

      let targetOption: Element | null = null;
      for (const option of allOptions) {
        const optionTextElement = option.querySelector(
          '.model-option-content span.gmat-body-medium, .mdc-list-item__primary-text'
        );
        if (optionTextElement) {
          const text = optionTextElement.textContent?.trim() || '';
          if (text.includes(modelDisplayName) || modelDisplayName.includes(text)) {
            targetOption = option;
            logger.info(`找到匹配选项: "${text}" for "${modelDisplayName}"`);
            break;
          }
        }
      }

      // 如果精确匹配失败，尝试模糊匹配
      if (!targetOption) {
        const simplifiedModelDisplayName = modelDisplayName.replace(/[\d.-]+$/, '').trim();
        for (const option of allOptions) {
          const optionTextElement = option.querySelector(
            '.model-option-content span.gmat-body-medium, .mdc-list-item__primary-text'
          );
          if (optionTextElement) {
            const text = optionTextElement.textContent?.trim() || '';
            if (text.toLowerCase().includes(simplifiedModelDisplayName.toLowerCase())) {
              targetOption = option;
              logger.info(`通过模糊匹配找到选项: "${text}" for simplified "${simplifiedModelDisplayName}"`);
              break;
            }
          }
        }
      }

      if (!targetOption) {
        logger.error(`在选项中未找到模型 "${modelDisplayName}". 可用选项:`);
        allOptions.forEach((opt) => {
          const optTextEl = opt.querySelector(
            '.model-option-content span.gmat-body-medium, .mdc-list-item__primary-text'
          );
          if (optTextEl) {
            logger.info(` - ${optTextEl.textContent?.trim()}`);
          }
        });
        // 尝试关闭下拉框
        simulateClick(studioModelSelector);
        throw new DOMError(`在选项中未找到模型 "${modelDisplayName}"`);
      }

      // 4. 点击找到的选项
      simulateClick(targetOption);
      await delay(300); // 等待选择生效

      logger.info(`AI Studio 模型已成功切换为 ${modelDisplayName} (ID: ${modelId})`);
    } catch (error) {
      throw new DOMError(
        `模型切换失败: ${error instanceof Error ? error.message : String(error)}`,
        { modelId, originalError: error }
      );
    }
  }

  /**
   * 设置生成参数
   */
  public async setGenerationConfig(config: GenerationConfig): Promise<void> {
    try {
      logger.info('设置生成参数:', config);

      if (config.temperature !== undefined) {
        await this.setTemperature(config.temperature);
      }

      if (config.topP !== undefined) {
        await this.setTopP(config.topP);
      }

      if (config.maxOutputTokens !== undefined) {
        await this.setMaxTokens(config.maxOutputTokens);
      }

      if (config.stopSequences !== undefined) {
        await this.setStopSequences(config.stopSequences);
      }

      logger.info('生成参数设置完成');
    } catch (error) {
      throw new DOMError(
        `设置生成参数失败: ${error instanceof Error ? error.message : String(error)}`,
        { config, originalError: error }
      );
    }
  }

  /**
   * 设置温度参数 - 按照原脚本的逻辑实现
   */
  private async setTemperature(temperature: number): Promise<void> {
    try {
      logger.info(`正在设置 temperature 为 ${temperature}...`);

      let targetElement = document.querySelector(
        'div[data-test-id="temperatureSliderContainer"] input[type="number"].slider-input'
      ) as HTMLInputElement;

      if (!targetElement) {
        // Fallback if specific selector fails
        const labels = Array.from(document.querySelectorAll('h3.gmat-body-medium'));
        for (const label of labels) {
          if (label.textContent?.trim().toLowerCase() === 'temperature') {
            targetElement = label
              .closest('.settings-item-column')
              ?.querySelector('input[type="number"].slider-input') as HTMLInputElement;
            if (targetElement) break;
          }
        }
      }

      if (targetElement) {
        this.fillInputField(targetElement, temperature.toString());
        logger.info('temperature 设置成功！');
      } else {
        logger.error('未找到 AI Studio 页面上 temperature 对应的DOM元素');
      }

      await delay(TIMEOUTS.PARAMETER_SET_DELAY);
    } catch (error) {
      logger.error(`设置 temperature 失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 设置 Top P 参数 - 按照原脚本的逻辑实现
   */
  private async setTopP(topP: number): Promise<void> {
    try {
      logger.info(`正在设置 topP 为 ${topP}...`);

      // 查找所有 h3.gmat-body-medium 元素
      const allH3s = document.querySelectorAll('h3.gmat-body-medium');
      let topPLabel: Element | null = null;
      for (const h3 of allH3s) {
        if (h3.textContent?.trim().toLowerCase() === 'top p') {
          topPLabel = h3;
          break;
        }
      }

      let targetElement: HTMLInputElement | null = null;
      if (topPLabel) {
        const parentColumn = topPLabel.closest('.settings-item-column');
        if (parentColumn) {
          targetElement = parentColumn.querySelector(
            'input[type="number"].slider-input'
          ) as HTMLInputElement;
        }
      }

      if (!targetElement) {
        // 保留原始的 fallback 逻辑
        const labels = Array.from(document.querySelectorAll('h3.gmat-body-medium'));
        for (const label of labels) {
          if (label.textContent?.trim().toLowerCase() === 'top p') {
            targetElement = label
              .closest('.settings-item-column')
              ?.querySelector('input[type="number"].slider-input') as HTMLInputElement;
            if (targetElement) break;
          }
        }
      }

      if (targetElement) {
        this.fillInputField(targetElement, topP.toString());
        logger.info('topP 设置成功！');
      } else {
        logger.error('未找到 AI Studio 页面上 topP 对应的DOM元素');
      }

      await delay(TIMEOUTS.PARAMETER_SET_DELAY);
    } catch (error) {
      logger.error(`设置 topP 失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 设置最大输出令牌数 - 按照原脚本的逻辑实现
   */
  private async setMaxTokens(maxTokens: number): Promise<void> {
    try {
      logger.info(`正在设置 maxOutputTokens 为 ${maxTokens}...`);

      let targetElement = document.querySelector(
        'input[aria-label="Maximum output tokens"]'
      ) as HTMLInputElement;

      if (!targetElement) {
        // Fallback
        const labels = Array.from(document.querySelectorAll('h3.gmat-body-medium'));
        for (const label of labels) {
          if (label.textContent?.trim().toLowerCase() === 'output length') {
            // "Output length" seems to be the label for max tokens
            targetElement = label
              .closest('.settings-item-column, .settings-item')
              ?.querySelector('input[type="number"]') as HTMLInputElement;
            if (targetElement) break;
          }
        }
      }

      if (targetElement) {
        this.fillInputField(targetElement, maxTokens.toString());
        logger.info('maxOutputTokens 设置成功！');
      } else {
        logger.error('未找到 AI Studio 页面上 maxOutputTokens 对应的DOM元素');
      }

      await delay(TIMEOUTS.PARAMETER_SET_DELAY);
    } catch (error) {
      logger.error(`设置 maxOutputTokens 失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 设置停止序列 - 按照原脚本的逻辑实现
   */
  private async setStopSequences(stopSequences: string[]): Promise<void> {
    try {
      logger.info(`正在设置 stopSequences 为 ${JSON.stringify(stopSequences)}...`);

      // 停止序列比较特殊，它是一个chips input
      const stopInput = document.querySelector(
        'input[aria-label="Add stop token"]'
      ) as HTMLInputElement;

      if (!stopInput) {
        logger.error('未找到 AI Studio 页面上 stopSequences 对应的DOM元素');
        return;
      }

      const existingChips = document.querySelectorAll(
        'mat-chip-grid mat-chip-row button[aria-label*="Remove"]'
      );

      // 1. 清除现有停止序列
      for (const chipRemoveButton of existingChips) {
        simulateClick(chipRemoveButton);
        await delay(50); // 短暂等待移除生效
      }

      // 2. 添加新的停止序列
      if (stopSequences && Array.isArray(stopSequences)) {
        for (const seq of stopSequences) {
          if (seq.trim()) {
            stopInput.value = seq.trim();
            stopInput.dispatchEvent(new Event('input', { bubbles: true }));
            // 模拟回车添加
            const enterEvent = new KeyboardEvent('keydown', {
              key: 'Enter',
              code: 'Enter',
              bubbles: true,
            });
            stopInput.dispatchEvent(enterEvent);
            await delay(100); // 等待chip添加
          }
        }
      }

      logger.info('stopSequences 设置成功！');
    } catch (error) {
      logger.error(`设置 stopSequences 失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取响应文本
   */
  public async getResponseText(): Promise<string> {
    try {
      if (!this.elements.responseContainer) {
        this.elements.responseContainer = await this.findElement(
          DOM_SELECTORS.responseContainer
        ) as HTMLElement;
      }

      const textElement = this.elements.responseContainer.querySelector(DOM_SELECTORS.responseText);
      if (!textElement) {
        return '';
      }

      return textElement.textContent?.trim() || '';
    } catch (error) {
      logger.warn('获取响应文本失败:', error);
      return '';
    }
  }

  /**
   * 检查是否正在生成
   */
  public isGenerating(): boolean {
    try {
      const spinner = document.querySelector(DOM_SELECTORS.loadingSpinner);
      return spinner !== null && isElementVisible(spinner);
    } catch {
      return false;
    }
  }

  /**
   * 等待生成完成
   */
  public async waitForGenerationComplete(): Promise<void> {
    const startTime = Date.now();

    while (this.isGenerating()) {
      if (Date.now() - startTime > TIMEOUTS.RESPONSE_TIMEOUT) {
        throw new DOMError('等待生成完成超时');
      }

      await delay(TIMEOUTS.RESPONSE_POLL_INTERVAL);
    }
  }

  /**
   * 设置模型列表
   */
  public setModelList(models: ModelInfo[]): void {
    this.modelList = models;
    logger.debug(`设置模型列表: ${models.length} 个模型`);
  }

  /**
   * 获取当前页面状态
   */
  public getPageState(): { isReady: boolean; hasInput: boolean; isGenerating: boolean } {
    return {
      isReady: this.isInitialized,
      hasInput: !!this.elements.promptInput && isElementInteractable(this.elements.promptInput),
      isGenerating: this.isGenerating(),
    };
  }

  /**
   * 刷新元素引用
   */
  public async refreshElements(): Promise<void> {
    this.elements = {};
    await this.findAllElements();
  }

  /**
   * 获取当前页面的参数值
   */
  public getCurrentParameters(): GenerationConfig {
    const config: GenerationConfig = {};

    try {
      // 获取温度值
      const tempInput = document.querySelector(
        'div[data-test-id="temperatureSliderContainer"] input[type="number"].slider-input'
      ) as HTMLInputElement;
      if (tempInput && tempInput.value) {
        config.temperature = parseFloat(tempInput.value);
      }

      // 获取 Top P 值
      const allH3s = document.querySelectorAll('h3.gmat-body-medium');
      for (const h3 of allH3s) {
        if (h3.textContent?.trim().toLowerCase() === 'top p') {
          const parentColumn = h3.closest('.settings-item-column');
          if (parentColumn) {
            const topPInput = parentColumn.querySelector(
              'input[type="number"].slider-input'
            ) as HTMLInputElement;
            if (topPInput && topPInput.value) {
              config.topP = parseFloat(topPInput.value);
            }
          }
          break;
        }
      }

      // 获取最大令牌数
      const maxTokensInput = document.querySelector(
        'input[aria-label="Maximum output tokens"]'
      ) as HTMLInputElement;
      if (maxTokensInput && maxTokensInput.value) {
        config.maxOutputTokens = parseInt(maxTokensInput.value, 10);
      }

      // 获取停止序列
      const stopChips = document.querySelectorAll('mat-chip-grid mat-chip-row');
      const stopSequences: string[] = [];
      for (const chip of stopChips) {
        const text = chip.textContent?.trim();
        if (text && !text.includes('Remove')) {
          stopSequences.push(text.replace(/\s*Remove\s*$/, '').trim());
        }
      }
      if (stopSequences.length > 0) {
        config.stopSequences = stopSequences;
      }
    } catch (error) {
      logger.warn('获取当前参数失败:', error);
    }

    return config;
  }

  /**
   * 获取当前选中的模型
   */
  public getCurrentModel(): string | null {
    try {
      const modelSelector = document.querySelector('mat-select[data-test-ms-model-selector]');
      if (modelSelector) {
        const selectedText = modelSelector.textContent?.trim();
        if (selectedText) {
          // 尝试从模型列表中找到匹配的模型ID
          const matchedModel = this.modelList.find(m =>
            m.displayName === selectedText || m.name === selectedText
          );
          return matchedModel ? matchedModel.id : null;
        }
      }
    } catch (error) {
      logger.warn('获取当前模型失败:', error);
    }
    return null;
  }

  /**
   * 填充输入字段 - 模拟 Playwright 的 fill 方法
   */
  private fillInputField(element: HTMLInputElement, value: string): void {
    // 清空现有值
    element.value = '';
    element.dispatchEvent(new Event('input', { bubbles: true }));

    // 设置新值
    element.value = value;

    // 触发事件序列，确保页面响应
    element.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
    element.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));

    // 触发焦点事件
    element.dispatchEvent(new Event('blur', { bubbles: true }));

    logger.debug(`填充输入字段: ${value}`);
  }
}
