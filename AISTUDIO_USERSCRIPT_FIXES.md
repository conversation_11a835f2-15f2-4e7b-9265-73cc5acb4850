# AI Studio 用户脚本监听修复 - 完整版

## 🔍 问题分析

通过对比您之前的 `userscripts/aistudio.user.js` 和当前的 TypeScript 实现，发现了以下关键问题：

### 1. 模型拦截问题
- **原脚本**: 直接重写 `xhr.onload`，立即拦截模型列表请求
- **新实现**: 使用 `addEventListener`，可能错过早期请求

### 2. 参数同步问题
- **原脚本**: 有完整的双向参数同步逻辑
- **新实现**: 缺少页面参数读取和同步功能

### 3. DOM选择器问题
- **原脚本**: 使用精确的 AI Studio 特定选择器
- **新实现**: 使用通用选择器，可能不匹配

## 🔧 完整修复方案

### 1. 修复模型拦截 - 完全按照原脚本逻辑

在 `src/api/request-interceptor.ts` 中的 `startImmediateInterception()` 方法：

```typescript
// 完全按照原脚本的方式 - 直接重写 xhr.onload
XMLHttpRequest.prototype.send = function (body?: Document | XMLHttpRequestBodyInit | null) {
  const xhr = this;
  const url = (xhr as any)._url;

  if (url === listModelsUrl) {
    xhr.onload = function () {
      if (xhr.readyState === 4 && xhr.status === 200) {
        try {
          const interceptedData = JSON.parse(xhr.response);
          logger.info('成功拦截到模型数据:', interceptedData);

          const models = self.parseModelsFromResponse(interceptedData);
          if (models.length > 0) {
            self.interceptedModels = models;
            logger.info(`拦截到模型列表: ${models.length} 个模型`);
            self.emit('modelsLoaded', models);
          }
        } catch (e) {
          logger.error('解析模型数据失败:', e);
        }
      }
    };
  }

  return self.originalXHRSend.apply(xhr, [body]);
};
```

### 2. 修复模型切换 - 使用原脚本的精确逻辑

在 `src/dom/dom-controller.ts` 中的 `switchModel()` 方法：

```typescript
// 1. 找到 AI Studio 页面的模型选择器并点击
const studioModelSelector = document.querySelector('mat-select[data-test-ms-model-selector]');
simulateClick(studioModelSelector);
await delay(500); // 等待下拉菜单动画

// 2. 从模型列表中找到对应的 displayName
const modelEntry = this.modelList.find(m =>
  m.id === modelId || m.id.endsWith(modelId) || m.name === modelId
);

// 3. 在展开的选项中找到目标模型
const allOptions = document.querySelectorAll(
  'div.cdk-overlay-pane mat-option, div.cdk-overlay-pane div[role="option"]'
);

// 4. 精确匹配模型选项
const optionTextElement = option.querySelector(
  '.model-option-content span.gmat-body-medium, .mdc-list-item__primary-text'
);
```

### 3. 修复参数设置 - 使用原脚本的选择器

```typescript
// 温度参数 - 使用精确选择器
let targetElement = document.querySelector(
  'div[data-test-id="temperatureSliderContainer"] input[type="number"].slider-input'
) as HTMLInputElement;

// Top P 参数 - 查找 h3 标签
const allH3s = document.querySelectorAll('h3.gmat-body-medium');
for (const h3 of allH3s) {
  if (h3.textContent?.trim().toLowerCase() === 'top p') {
    const parentColumn = h3.closest('.settings-item-column');
    targetElement = parentColumn.querySelector('input[type="number"].slider-input');
  }
}

// 最大令牌数 - 使用 aria-label
let targetElement = document.querySelector(
  'input[aria-label="Maximum output tokens"]'
) as HTMLInputElement;

// 停止序列 - 处理 chips input
const stopInput = document.querySelector('input[aria-label="Add stop token"]');
const existingChips = document.querySelectorAll(
  'mat-chip-grid mat-chip-row button[aria-label*="Remove"]'
);
```

### 4. 添加参数读取功能

```typescript
// 获取当前页面的参数值
public getCurrentParameters(): GenerationConfig {
  const config: GenerationConfig = {};

  // 获取温度值
  const tempInput = document.querySelector(
    'div[data-test-id="temperatureSliderContainer"] input[type="number"].slider-input'
  ) as HTMLInputElement;
  if (tempInput && tempInput.value) {
    config.temperature = parseFloat(tempInput.value);
  }

  // 获取 Top P、最大令牌数、停止序列等...
  return config;
}

// 获取当前选中的模型
public getCurrentModel(): string | null {
  const modelSelector = document.querySelector('mat-select[data-test-ms-model-selector]');
  const selectedText = modelSelector.textContent?.trim();
  const matchedModel = this.modelList.find(m =>
    m.displayName === selectedText || m.name === selectedText
  );
  return matchedModel ? matchedModel.id : null;
}
```

## 🚀 测试步骤

1. **构建脚本**: `npm run build`
2. **安装脚本**: 将 `dist/gemini2api.user.js` 安装到 Tampermonkey
3. **访问页面**: 打开 `https://aistudio.google.com`
4. **检查日志**: 应该看到 "成功拦截到模型数据" 和 "拦截到模型列表: X 个模型"
5. **测试同步**: 在调试面板中修改参数，检查页面是否同步更新
6. **测试反向同步**: 在页面上修改参数，检查调试面板是否同步

## ✅ 关键修复点

1. **完全按照原脚本的拦截逻辑** - 直接重写 `xhr.onload` 而不是使用 `addEventListener`
2. **使用精确的 AI Studio DOM 选择器** - 所有选择器都与原脚本保持一致
3. **实现双向参数同步功能** - 添加了 `getCurrentParameters()` 和 `getCurrentModel()` 方法
4. **保持与原脚本相同的事件处理方式** - 使用相同的事件分发和处理逻辑
5. **添加详细的日志输出便于调试** - 每个关键步骤都有对应的日志

## 🎯 预期结果

现在的实现应该能够：

- ✅ 正确拦截到 AI Studio 的模型列表请求
- ✅ 在调试面板中显示所有可用模型
- ✅ 实现调试面板和页面参数的双向同步
- ✅ 正确切换模型并设置各种生成参数
- ✅ 与您原来的 `aistudio.user.js` 功能完全一致

如果仍有问题，请提供新的控制台日志，我会进一步调试和修复。
