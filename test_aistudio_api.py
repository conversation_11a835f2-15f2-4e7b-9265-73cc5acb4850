#!/usr/bin/env python3
"""
AI Studio 2 API - Test Script

测试 AI Studio API 功能
"""

import asyncio
import json
import requests
import websockets
from datetime import datetime

# 服务器配置
SERVER_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws/aistudio"

async def test_websocket_connection():
    """测试 WebSocket 连接"""
    print("测试 WebSocket 连接...")
    
    try:
        async with websockets.connect(WS_URL) as websocket:
            print("✓ WebSocket 连接成功")
            
            # 发送用户脚本就绪消息
            ready_message = {
                "type": "userscript_ready",
                "data": {
                    "models": [
                        {
                            "id": "models/gemini-2.0-flash",
                            "name": "gemini-2.0-flash",
                            "displayName": "Gemini 2.0 Flash",
                            "description": "Fast and efficient model"
                        }
                    ],
                    "capabilities": [
                        "text_generation",
                        "model_switching",
                        "parameter_control",
                        "stream_response"
                    ],
                    "version": "0.0.1"
                },
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(ready_message))
            print("✓ 发送用户脚本就绪消息")
            
            # 等待服务器响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                response_data = json.loads(response)
                print(f"✓ 收到服务器响应: {response_data.get('type', 'unknown')}")
            except asyncio.TimeoutError:
                print("⚠ 等待服务器响应超时")
            
            return True
            
    except Exception as e:
        print(f"✗ WebSocket 连接失败: {e}")
        return False

def test_api_endpoints():
    """测试 API 端点"""
    print("\n测试 API 端点...")
    
    # 测试根端点
    try:
        response = requests.get(f"{SERVER_URL}/")
        if response.status_code == 200:
            print("✓ 根端点正常")
        else:
            print(f"✗ 根端点错误: {response.status_code}")
    except Exception as e:
        print(f"✗ 根端点请求失败: {e}")
    
    # 测试模型列表端点
    try:
        response = requests.get(f"{SERVER_URL}/v1/models")
        if response.status_code == 200:
            models = response.json()
            print(f"✓ 模型列表端点正常，返回 {len(models.get('data', []))} 个模型")
        else:
            print(f"✗ 模型列表端点错误: {response.status_code}")
    except Exception as e:
        print(f"✗ 模型列表端点请求失败: {e}")

def test_chat_completion_without_userscript():
    """测试没有用户脚本连接时的聊天完成"""
    print("\n测试聊天完成 API（无用户脚本）...")
    
    chat_request = {
        "model": "gemini-2.0-flash",
        "messages": [
            {"role": "user", "content": "Hello, how are you?"}
        ],
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    try:
        response = requests.post(
            f"{SERVER_URL}/v1/aistudio/chat/completions",
            json=chat_request,
            timeout=10
        )
        
        if response.status_code == 503:
            print("✓ 正确返回 503 错误（无用户脚本连接）")
        else:
            print(f"✗ 意外的状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"✗ 聊天完成请求失败: {e}")

async def simulate_userscript_response(websocket, request_id):
    """模拟用户脚本响应"""
    await asyncio.sleep(1)  # 模拟处理时间
    
    # 发送模拟响应
    response_message = {
        "type": "api_response",
        "requestId": request_id,
        "data": {
            "content": "Hello! I'm doing well, thank you for asking. How can I help you today?",
            "finishReason": "stop",
            "usage": {
                "promptTokenCount": 10,
                "candidatesTokenCount": 20,
                "totalTokenCount": 30
            }
        },
        "timestamp": datetime.now().isoformat()
    }
    
    await websocket.send(json.dumps(response_message))
    print("✓ 发送模拟响应")

async def test_chat_completion_with_userscript():
    """测试有用户脚本连接时的聊天完成"""
    print("\n测试聊天完成 API（有用户脚本）...")
    
    try:
        async with websockets.connect(WS_URL) as websocket:
            # 发送就绪消息
            ready_message = {
                "type": "userscript_ready",
                "data": {
                    "models": [
                        {
                            "id": "models/gemini-2.0-flash",
                            "name": "gemini-2.0-flash",
                            "displayName": "Gemini 2.0 Flash"
                        }
                    ],
                    "capabilities": ["text_generation"],
                    "version": "0.0.1"
                }
            }
            await websocket.send(json.dumps(ready_message))
            
            # 等待确认
            await websocket.recv()
            
            # 启动监听任务
            async def listen_for_requests():
                while True:
                    try:
                        message = await websocket.recv()
                        data = json.loads(message)
                        
                        if data.get("type") == "api_request":
                            request_id = data.get("requestId")
                            print(f"✓ 收到 API 请求: {request_id}")
                            
                            # 发送模拟响应
                            await simulate_userscript_response(websocket, request_id)
                            break
                            
                    except Exception as e:
                        print(f"监听请求时出错: {e}")
                        break
            
            # 启动监听任务
            listen_task = asyncio.create_task(listen_for_requests())
            
            # 等待一下让 WebSocket 连接稳定
            await asyncio.sleep(0.5)
            
            # 发送聊天请求
            chat_request = {
                "model": "gemini-2.0-flash",
                "messages": [
                    {"role": "user", "content": "Hello, how are you?"}
                ],
                "temperature": 0.7,
                "max_tokens": 100
            }
            
            try:
                response = requests.post(
                    f"{SERVER_URL}/v1/aistudio/chat/completions",
                    json=chat_request,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print("✓ 聊天完成请求成功")
                    print(f"响应内容: {result.get('choices', [{}])[0].get('message', {}).get('content', 'N/A')}")
                else:
                    print(f"✗ 聊天完成请求失败: {response.status_code}")
                    print(f"错误内容: {response.text}")
                    
            except Exception as e:
                print(f"✗ 聊天完成请求异常: {e}")
            
            # 等待监听任务完成
            try:
                await asyncio.wait_for(listen_task, timeout=5.0)
            except asyncio.TimeoutError:
                listen_task.cancel()
                
    except Exception as e:
        print(f"✗ 测试失败: {e}")

async def main():
    """主测试函数"""
    print("AI Studio 2 API 测试开始\n")
    print("=" * 50)
    
    # 测试 WebSocket 连接
    ws_success = await test_websocket_connection()
    
    # 测试 API 端点
    test_api_endpoints()
    
    # 测试无用户脚本的聊天完成
    test_chat_completion_without_userscript()
    
    # 如果 WebSocket 连接成功，测试有用户脚本的聊天完成
    if ws_success:
        await test_chat_completion_with_userscript()
    else:
        print("\n⚠ 跳过用户脚本测试（WebSocket 连接失败）")
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    asyncio.run(main())
