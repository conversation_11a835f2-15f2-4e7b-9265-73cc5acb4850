import datetime
import json

from flask import Flask, jsonify, request

app = Flask(__name__)


@app.route("/save_message", methods=["POST"])
def save_message():
    if request.method != "POST":
        return jsonify({"error": "Method Not Allowed"}), 405
    try:
        message_data = request.get_json()
        if message_data is None:
            return jsonify({"error": "Invalid JSON or missing Content-Type header"}), 400
    except Exception as e:
        return jsonify({"error": f"Error parsing JSON: {e}"}), 400

    with open("saved.txt", "a", encoding="utf-8") as f:
        f.write(message_data + "\n")

    return jsonify({"message": "Message saved successfully!"})


if __name__ == "__main__":
    app.run(host="localhost", port=8001)
