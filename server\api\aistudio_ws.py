"""
AI Studio 2 API - WebSocket Endpoints

AI Studio 用户脚本的 WebSocket 连接处理
"""

import json
import logging
from typing import Dict, Any

from fastapi import WebSocket, WebSocketDisconnect

from server.core.connection_manager import manager

logger = logging.getLogger(__name__)

async def handle_aistudio_userscript_websocket(websocket: WebSocket):
    """处理 AI Studio 用户脚本的 WebSocket 连接"""
    
    user_id = "default"  # 可以从查询参数或头部获取用户ID
    
    try:
        await manager.connect_aistudio_userscript(websocket, user_id)
        
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理消息
                await handle_userscript_message(message, user_id)
                
            except json.JSONDecodeError as e:
                logger.error(f"解析用户脚本消息失败: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "error": "Invalid JSON format"
                }))
            except Exception as e:
                logger.error(f"处理用户脚本消息失败: {e}")
                break
                
    except WebSocketDisconnect:
        logger.info(f"AI Studio 用户脚本 WebSocket 断开连接: {user_id}")
    except Exception as e:
        logger.error(f"AI Studio 用户脚本 WebSocket 错误: {e}")
    finally:
        manager.disconnect_aistudio_userscript(websocket, user_id)

async def handle_userscript_message(message: Dict[str, Any], user_id: str):
    """处理来自用户脚本的消息"""
    
    message_type = message.get("type")
    
    try:
        if message_type == "userscript_ready":
            await handle_userscript_ready(message, user_id)
        elif message_type in ["api_response", "api_stream_chunk", "api_stream_end", "api_error"]:
            # 转发到 AI Studio API 处理器
            manager.handle_aistudio_message(message)
        elif message_type == "ping":
            # 心跳响应
            await manager.send_to_aistudio_userscript({
                "type": "pong",
                "timestamp": message.get("timestamp")
            }, user_id)
        else:
            logger.warning(f"未知消息类型: {message_type}")
            
    except Exception as e:
        logger.error(f"处理用户脚本消息失败: {e}")

async def handle_userscript_ready(message: Dict[str, Any], user_id: str):
    """处理用户脚本就绪消息"""
    
    data = message.get("data", {})
    models = data.get("models", [])
    capabilities = data.get("capabilities", [])
    version = data.get("version", "unknown")
    
    logger.info(f"AI Studio 用户脚本就绪: {user_id}, 版本: {version}")
    logger.info(f"支持的功能: {capabilities}")
    logger.info(f"可用模型: {len(models)} 个")
    
    # 更新模型列表
    if models:
        manager.set_aistudio_models(models)
    
    # 发送确认消息
    await manager.send_to_aistudio_userscript({
        "type": "connection_status",
        "data": {
            "status": "connected",
            "message": "用户脚本连接成功",
            "server_version": "1.0.0"
        }
    }, user_id)
