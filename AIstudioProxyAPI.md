# AIstudioProxyAPI 的运行逻辑和数据交互机制分析报告

主要根据`references\AIstudioProxyAPI\server.py`。

**核心运行逻辑:**
该服务通过 FastAPI 暴露 API 接口，并使用 Playwright 驱动无头浏览器模拟用户在 Google AI Studio 网页上的操作。所有请求通过一个异步队列进行顺序处理，以避免浏览器并发操作的冲突。

**模拟提交信息（发送数据）:**
1.  **提示准备:** 将标准 API 请求中的 `messages`（包含多轮对话历史、系统指令、工具调用等）格式化为 AI Studio 网页输入所需的单一文本字符串。
2.  **页面操作:**
    *   在每次新请求前尝试清空 AI Studio 的聊天记录。
    *   根据请求参数，通过 Playwright 模拟用户操作 AI Studio 界面上的温度、最大输出 Token 和停止序列等设置项（包括输入文本、点击移除按钮等）。
    *   将准备好的提示文本注入到 AI Studio 的输入框中。
    *   模拟键盘快捷键（Ctrl/Meta + Enter）来提交提示。
3.  **模型切换:** 如果请求指定了特定模型，服务会尝试修改 AI Studio 的 `localStorage` 并重新加载页面以切换模型。

**获取信息（接收数据）:**
1.  **响应等待:** 持续轮询 AI Studio 页面状态，通过检查输入框是否清空、提交按钮状态以及“编辑”按钮是否出现来判断响应是否生成完成。
2.  **内容提取:**
    *   优先尝试通过模拟点击助手消息旁的“编辑”按钮，从弹出的文本区域中读取响应内容。
    *   如果“编辑”方法失败，则回退到模拟点击“更多选项”中的“复制 Markdown”按钮，并从浏览器剪贴板中读取内容。
3.  **辅助服务:** 服务支持集成外部辅助服务。如果配置了相关环境变量，则会直接从辅助服务获取流式或非流式响应，从而跳过 Playwright 的页面抓取步骤，提高效率。
4.  **响应返回:** 根据请求类型，将获取到的内容格式化为伪流式 SSE 响应或完整的 JSON 响应，并返回给请求方。
5.  **错误处理:** 在整个交互过程中，服务会持续监控 AI Studio 页面上可能出现的错误提示，并将其捕获并作为 HTTP 异常返回。

该代理服务的健壮性依赖于对 AI Studio 网页结构的准确识别和模拟，任何 UI 变化都可能导致其功能受损。

**使用的页面元素**
PROMPT_TEXTAREA_SELECTOR: 提示文本区域，用于输入用户消息。
SUBMIT_BUTTON_SELECTOR: 提交按钮，用于发送消息。
CLEAR_CHAT_BUTTON_SELECTOR: 清除聊天记录按钮，用于清除之前的聊天记录。
CLEAR_CHAT_CONFIRM_BUTTON_SELECTOR: 清除聊天记录确认按钮，用于确认清除聊天记录。
TEMPERATURE_INPUT_SELECTOR: 温度输入框，用于调整温度参数。
MAX_OUTPUT_TOKENS_SELECTOR: 最大输出 tokens 输入框，用于调整最大输出 tokens 参数。
STOP_SEQUENCE_INPUT_SELECTOR: 停止序列输入框，用于设置停止序列参数。
MAT_CHIP_REMOVE_BUTTON_SELECTOR: 移除停止序列的按钮。
TOP_P_INPUT_SELECTOR: Top P 输入框，用于调整 Top P 参数。
RESPONSE_CONTAINER_SELECTOR: 响应容器，用于定位 AI Studio 的响应内容。
RESPONSE_TEXT_SELECTOR: 响应文本选择器，用于从响应容器中提取文本内容。
MODEL_ERROR_CONTAINER_SELECTOR: 模型错误容器，用于检测模型返回的错误信息。
ERROR_TOAST_SELECTOR: 页面 Toast 错误提示，用于检测页面错误。